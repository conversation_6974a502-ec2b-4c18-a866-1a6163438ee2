# Database Connectivity Troubleshooting Guide

This guide helps diagnose and fix database connectivity issues during FES CRM Backend deployment.

## Quick Diagnosis

Run the database connectivity test script:

```bash
cd /root/fes-crm-backend
chmod +x scripts/test-db-connectivity.sh
./scripts/test-db-connectivity.sh
```

## Common Issues and Solutions

### 1. Environment File Issues

**Problem**: `Environment file (.env) not found` or `DATABASE_URL missing`

**Solution**:
```bash
# Check if .env file exists
ls -la /root/fes-crm-backend/.env

# Check DATABASE_URL in .env
grep DATABASE_URL /root/fes-crm-backend/.env

# If missing, copy from backup or create new one
cp /tmp/.env.production.backup /root/fes-crm-backend/.env
# OR
cp .env.prod.example .env
# Then edit .env with correct DATABASE_URL
```

### 2. Container Not Running

**Problem**: `Container fes_crm_prod not found or not running`

**Solution**:
```bash
# Check container status
sudo docker compose -f docker-compose.prod.yml ps

# Start containers if not running
sudo docker compose -f docker-compose.prod.yml up -d

# Check logs for startup issues
sudo docker compose -f docker-compose.prod.yml logs backend
```

### 3. Database Connection Timeout

**Problem**: Database connectivity test fails with timeout errors

**Possible Causes**:
- External database server is down
- Network connectivity issues
- Firewall blocking connections
- Incorrect DATABASE_URL format

**Solution**:
```bash
# Test network connectivity to database host
# Extract host from DATABASE_URL (replace with your actual host)
ping dbaas-db-8482593-do-user-21444604-0.f.db.ondigitalocean.com

# Test port connectivity
telnet dbaas-db-8482593-do-user-21444604-0.f.db.ondigitalocean.com 25060

# Check DATABASE_URL format (should be without quotes in Docker)
# Correct format: DATABASE_URL=postgres://user:pass@host:port/db
# Incorrect: DATABASE_URL="postgres://user:pass@host:port/db"
```

### 4. Authentication Issues

**Problem**: Database authentication fails

**Solution**:
```bash
# Verify DATABASE_URL credentials
# Check if username/password are correct
# Ensure the database user has proper permissions

# Test connection manually (replace with your actual values)
psql "postgres://username:password@host:port/database" -c "SELECT 1;"
```

### 5. Prisma Schema Issues

**Problem**: `Prisma schema file not accessible`

**Solution**:
```bash
# Check if prisma directory exists in container
sudo docker compose -f docker-compose.prod.yml exec backend ls -la prisma/

# Check if schema.prisma exists
sudo docker compose -f docker-compose.prod.yml exec backend cat prisma/schema.prisma

# If missing, the Docker image might be corrupted
# Rebuild and redeploy
```

## Manual Testing Commands

### Test Database Connection from Container

```bash
# Basic connectivity test
sudo docker compose -f docker-compose.prod.yml exec backend npx prisma db pull --schema=./prisma/schema.prisma

# Check migration status
sudo docker compose -f docker-compose.prod.yml exec backend npx prisma migrate status --schema=./prisma/schema.prisma

# Test Prisma client connection
sudo docker compose -f docker-compose.prod.yml exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect().then(() => {
  console.log('✅ Database connection successful');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Database connection failed:', error.message);
  process.exit(1);
});
"
```

### Check Environment Variables

```bash
# Check all environment variables in container
sudo docker compose -f docker-compose.prod.yml exec backend printenv

# Check specific database-related variables
sudo docker compose -f docker-compose.prod.yml exec backend printenv | grep -E "(DATABASE|NODE_ENV|BACKEND_PORT)"

# Check if DATABASE_URL is properly formatted
sudo docker compose -f docker-compose.prod.yml exec backend echo $DATABASE_URL
```

### Check Container Health

```bash
# Check container status
sudo docker compose -f docker-compose.prod.yml ps

# Check container logs
sudo docker compose -f docker-compose.prod.yml logs backend

# Check container resource usage
sudo docker stats fes_crm_prod

# Check container health status
sudo docker inspect fes_crm_prod | grep -A 10 Health
```

## Production Database Configuration

For DigitalOcean PostgreSQL (production):

```env
# .env file format (no quotes around DATABASE_URL)
DATABASE_URL=postgres://username:<EMAIL>:25060/database_name
```

**Important Notes**:
- Do NOT wrap DATABASE_URL in quotes when using Docker
- Ensure the database user has proper permissions
- Check that the database server allows connections from your server's IP
- Verify SSL requirements if applicable

## Emergency Recovery

If database connectivity completely fails:

1. **Check if it's a temporary issue**:
   ```bash
   # Wait and retry
   sleep 30
   ./scripts/test-db-connectivity.sh
   ```

2. **Rollback to previous version**:
   ```bash
   # Find latest backup
   ls -la /root/fes-crm-backend-backup-*
   
   # Restore from backup
   cd /root
   sudo docker compose -f fes-crm-backend/docker-compose.prod.yml down
   rm -rf fes-crm-backend
   cp -r fes-crm-backend-backup-YYYYMMDD-HHMMSS fes-crm-backend
   cd fes-crm-backend
   sudo docker compose -f docker-compose.prod.yml up -d
   ```

3. **Contact database administrator**:
   - Check DigitalOcean database status
   - Verify database server health
   - Check for maintenance windows

## Getting Help

If issues persist:

1. Run the full diagnostic script: `./scripts/test-db-connectivity.sh`
2. Collect logs: `sudo docker compose -f docker-compose.prod.yml logs backend > debug.log`
3. Check GitHub Actions logs for deployment details
4. Contact the development team with diagnostic output

## Prevention

To prevent future database connectivity issues:

1. **Monitor database health**: Set up monitoring for the DigitalOcean database
2. **Regular backups**: Ensure database backups are working
3. **Test connectivity**: Run connectivity tests during maintenance windows
4. **Update credentials**: Rotate database credentials regularly and update .env files
5. **Network monitoring**: Monitor network connectivity between server and database
