#!/bin/bash

# Database Connectivity Test Script for FES CRM Backend
# This script helps diagnose database connectivity issues during deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Configuration
CONTAINER_NAME="fes_crm_prod"
COMPOSE_FILE="docker-compose.prod.yml"

show_help() {
    echo "Database Connectivity Test Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -c, --container NAME    Container name (default: fes_crm_prod)"
    echo "  -f, --compose-file FILE Compose file (default: docker-compose.prod.yml)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                     # Test with default settings"
    echo "  $0 -c my_container     # Test specific container"
    echo "  $0 -f docker-compose.test.yml  # Use different compose file"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--container)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -f|--compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

log "Starting database connectivity tests..."
info "Container: $CONTAINER_NAME"
info "Compose file: $COMPOSE_FILE"

# Test 1: Check if .env file exists and has DATABASE_URL
log "Test 1: Checking environment configuration..."
if [ ! -f ".env" ]; then
    error "Environment file (.env) not found"
    exit 1
fi

if ! grep -q "DATABASE_URL" .env; then
    error "DATABASE_URL not found in .env file"
    exit 1
fi

# Mask sensitive parts of DATABASE_URL for logging
MASKED_DB_URL=$(grep "DATABASE_URL" .env | sed 's/:[^:]*@/:***@/g')
info "DATABASE_URL format: $MASKED_DB_URL"
log "✅ Environment configuration check passed"

# Test 2: Check container status
log "Test 2: Checking container status..."
if ! sudo docker compose -f "$COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME"; then
    error "Container $CONTAINER_NAME not found or not running"
    info "Available containers:"
    sudo docker compose -f "$COMPOSE_FILE" ps
    exit 1
fi

CONTAINER_STATUS=$(sudo docker compose -f "$COMPOSE_FILE" ps --format "table {{.Name}}\t{{.Status}}" | grep "$CONTAINER_NAME" || echo "")
info "Container status: $CONTAINER_STATUS"
log "✅ Container status check passed"

# Test 3: Check if container can execute commands
log "Test 3: Testing container command execution..."
if ! sudo docker compose -f "$COMPOSE_FILE" exec -T backend echo "Container command test" > /dev/null 2>&1; then
    error "Cannot execute commands in backend container"
    sudo docker compose -f "$COMPOSE_FILE" ps
    sudo docker compose -f "$COMPOSE_FILE" logs --tail=20 backend
    exit 1
fi
log "✅ Container command execution test passed"

# Test 4: Check environment variables in container
log "Test 4: Checking environment variables in container..."
ENV_CHECK=$(sudo docker compose -f "$COMPOSE_FILE" exec -T backend printenv | grep DATABASE_URL || echo "")
if [ -z "$ENV_CHECK" ]; then
    error "DATABASE_URL not found in container environment"
    info "Available environment variables:"
    sudo docker compose -f "$COMPOSE_FILE" exec -T backend printenv | grep -E "(DATABASE|NODE_ENV|BACKEND_PORT)" || echo "No relevant environment variables found"
    exit 1
fi

# Mask the DATABASE_URL in container for security
MASKED_CONTAINER_DB_URL=$(echo "$ENV_CHECK" | sed 's/:[^:]*@/:***@/g')
info "Container DATABASE_URL: $MASKED_CONTAINER_DB_URL"
log "✅ Container environment variables check passed"

# Test 5: Check Prisma schema accessibility
log "Test 5: Checking Prisma schema accessibility..."
if ! sudo docker compose -f "$COMPOSE_FILE" exec -T backend ls -la prisma/schema.prisma > /dev/null 2>&1; then
    error "Prisma schema file not accessible in container"
    info "Prisma directory contents:"
    sudo docker compose -f "$COMPOSE_FILE" exec -T backend ls -la prisma/ || echo "Prisma directory not found"
    exit 1
fi
log "✅ Prisma schema accessibility check passed"

# Test 6: Database connectivity test
log "Test 6: Testing database connectivity..."
DB_TEST_OUTPUT=$(sudo docker compose -f "$COMPOSE_FILE" exec -T backend npx prisma db pull --schema=./prisma/schema.prisma 2>&1 || echo "DB_TEST_FAILED")

if echo "$DB_TEST_OUTPUT" | grep -q "DB_TEST_FAILED"; then
    error "Database connectivity test failed"
    info "Database test output:"
    echo "$DB_TEST_OUTPUT"
    
    # Additional debugging
    info "Container logs (last 20 lines):"
    sudo docker compose -f "$COMPOSE_FILE" logs --tail=20 backend || true
    exit 1
fi

log "✅ Database connectivity test passed"

# Test 7: Migration status check
log "Test 7: Checking migration status..."
MIGRATION_STATUS=$(sudo docker compose -f "$COMPOSE_FILE" exec -T backend npx prisma migrate status --schema=./prisma/schema.prisma 2>&1 || echo "MIGRATION_STATUS_FAILED")

if echo "$MIGRATION_STATUS" | grep -q "MIGRATION_STATUS_FAILED"; then
    warn "Migration status check failed"
    info "Migration status output:"
    echo "$MIGRATION_STATUS"
else
    info "Migration status:"
    echo "$MIGRATION_STATUS"
    log "✅ Migration status check completed"
fi

# Summary
log "🎉 All database connectivity tests completed successfully!"
info "Summary:"
echo "  ✅ Environment configuration"
echo "  ✅ Container status"
echo "  ✅ Container command execution"
echo "  ✅ Environment variables in container"
echo "  ✅ Prisma schema accessibility"
echo "  ✅ Database connectivity"
echo "  ✅ Migration status check"

log "Database connectivity is working properly!"
